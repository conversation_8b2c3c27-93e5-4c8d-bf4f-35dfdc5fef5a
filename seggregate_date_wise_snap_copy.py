import multiprocessing
import pickle
import pandas as pd
import logging
from arcticdb import Arctic
import os
import gc

store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
library = "nse/1_min/snap_file_may20_jan21/trd_ord"
month_range = "may20_jan21"
lib = store[library]
syms = lib.list_symbols()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()


def get_from_snap_file(sym):
    # if "NIFTY" in sym: return

    if os.path.exists(
        f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/"
    ):
        return

    tot_len = lib.get_description(sym).row_count
    logger.info(f"Started for {sym} having length {tot_len}")

    count = 2 if sym not in ["NIFTY", "BANKNIFTY"] else 45

    for ct in range(1, count + 1):
        logger.info(f"Started for {sym} for chunk {ct}")
        # Using the context manager to handle memory and file reading
        df = lib.read(
            sym, row_range=[(ct - 1) * tot_len // count, ct * tot_len // count]
        ).data
        for date, dfg in df.groupby(df.timestamp.dt.date):
            if not os.path.exists(
                f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ct}"
            ):
                os.makedirs(
                    f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ct}",
                    exist_ok=True,
                )
            dfg.to_parquet(
                f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ct}/{date}.parquet"
            )

        df = pd.DataFrame()
        logger.info(f"Completed for {sym} for chunk {ct}")

    logger.info(f"Completed for {sym}")


def combine_date_wise_snap(sym):
    logger.info(f"Started combining for {sym}")

    count = 2 if sym not in ["NIFTY", "BANKNIFTY"] else 45

    for ct in range(1, count + 1):
        logger.info(f"Started combining for {sym} for chunk {ct}")
        for file in os.listdir(
            f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ct}"
        ):
            if not os.path.exists(
                f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined/{file[:10]}.parquet"
            ):
                df_list = []
                for ctj in list(range(ct, count + 1)):
                    if os.path.exists(
                        f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ctj}/{file}"
                    ):
                        df_list.append(
                            pd.read_parquet(
                                f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ctj}/{file}"
                            )
                        )

                df = pd.concat(df_list).drop_duplicates()
                df = df.set_index("timestamp").sort_index()

                if not os.path.exists(
                    f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined"
                ):
                    os.makedirs(
                        f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined"
                    )
                df.to_parquet(
                    f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined/{file[:10]}.parquet"
                )
                del df
                del df_list
                gc.collect()

            for ctj in range(ct, count + 1):
                if os.path.exists(
                    f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ctj}/{file}"
                ):
                    os.remove(
                        f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ctj}/{file}"
                    )

        logger.info(f"Completed combining for {sym} for chunk {ct}")

    logger.info(f"Completed combining for {sym}")


def push_1sec_snap_to_arctic(sym):
    logger.info(f"Started pushing 1sec snap to arctic for {sym}")

    storea = Arctic(
        "s3://*************:9000:arctic-db?access=super&secret=doopersecret"
    )
    libft = storea["nse/1_sec/futures_snap/trd"]
    libfo = storea["nse/1_sec/futures_snap/ord"]
    libot = storea["nse/1_sec/options_snap/trd"]
    liboo = storea["nse/1_sec/options_snap/ord"]

    files = os.listdir(
        f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined"
    )
    files.sort()

    for file in files:
        try:
            df = pd.read_parquet(
                f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined/{file}"
            )
            dff = df[df.option_type.isna()]
            dfft = df[df.close > 0].drop(
                columns=["option_type", "strike_price", "ord_price"]
            )
            dffo = df[df.ord_price > 0].drop(
                columns=["option_type", "strike_price", "close", "volume"]
            )
            dfo = df[df.strike_price > 0]
            dfot = dfo[dfo.close > 0].drop(columns=["ord_price"])
            dfoo = dfo[dfo.ord_price > 0].drop(columns=["close", "volume"])

            if libft.has_symbol(sym):
                empty_df = libft.read(
                    sym, date_range=(pd.Timestamp(2007, 1, 1),pd.Timestamp(2007, 1, 1))
                ).data
                columns = empty_df.columns
                dfft = dfft[columns]
                for col in columns:
                    dfft[col] = dfft[col].astype(empty_df[col].dtype)
                libft.update(sym, dfft)
            else:
                libft.append(sym, dfft)

            if libfo.has_symbol(sym):
                empty_df = libfo.read(
                    sym, date_range=(pd.Timestamp(2007, 1, 1),pd.Timestamp(2007, 1, 1))
                ).data
                columns = empty_df.columns
                dffo = dffo[columns]
                for col in columns:
                    dffo[col] = dffo[col].astype(empty_df[col].dtype)
                libfo.update(sym, dffo)
            else:
                libfo.append(sym, dffo)

            if libot.has_symbol(sym):
                empty_df = libot.read(
                    sym, date_range=(pd.Timestamp(2007, 1, 1),pd.Timestamp(2007, 1, 1))
                ).data
                columns = empty_df.columns
                dfot = dfot[columns]
                for col in columns:
                    dfot[col] = dfot[col].astype(empty_df[col].dtype)
                libot.update(sym, dfot)
            else:
                libot.append(sym, dfot)

            if liboo.has_symbol(sym):
                empty_df = liboo.read(
                    sym, date_range=(pd.Timestamp(2007, 1, 1),pd.Timestamp(2007, 1, 1))
                ).data
                columns = empty_df.columns
                dfoo = dfoo[columns]
                for col in columns:
                    dfoo[col] = dfoo[col].astype(empty_df[col].dtype)
                liboo.update(sym, dfoo)
            else:
                liboo.append(sym, dfoo)

            os.remove(
                f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined/{file}"
            )
        except Exception as e:
            os.makedirs(
                f"/home/<USER>/repos/data_auditing/failed_pushing_1sec_snap/{sym}/",
                exist_ok=True,
            )
            with open(
                f"/home/<USER>/repos/data_auditing/failed_pushing_1sec_snap/{sym}/error.txt",
                "w",
            ) as f:
                f.write(f"Failed for {sym} for file {file} due to: {e}\n")


    logger.info(f"Completed pushing 1sec snap to arctic for {sym}")


# push_1sec_snap_to_arctic("NIFTY")

# get_from_snap_file("NIFTY")

# with open("syms_may20_jan21_206", "rb") as f:
#     syms=pickle.load(f)
# [get_from_snap_file(sym) for sym in syms]


# syms = os.listdir(
#     f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled"
# )

# with multiprocessing.Pool(15) as p:
#     p.map(combine_date_wise_snap, syms)


with multiprocessing.Pool(15) as p:
    p.map(push_1sec_snap_to_arctic, syms)

# push_1sec_snap_to_arctic('AMBUJACEM')