import gc
import json
import os
import logging
import pickle
import shutil
import sys
import time
from typing import Dict, List
import pandas as pd

from arcticdb import Arctic
from arcticdb.version_store.library import ReadRequest
from arcticdb.version_store.library import ReadInfoRequest
from arcticdb.version_store.library import Library
from arcticdb_ext.version_store import DataError
from arcticdb_ext import set_config_int


class BackupConfig:
    """Centralized configuration for backup operations."""

    def __init__(self):
        """Initialize backup configuration from environment variables."""
        self.MOUNT_POINT = os.environ.get(
            "BACKUP_MOUNT_POINT", "/mnt/Qnap_nas/arctic_backups"
        )
        self.LIBRARIES_TO_SKIP = os.environ.get(
            "BACKUP_SKIP_LIBRARIES", "sampler,compilation"
        ).split(",")
        self.LIBRARIES_TO_SYNC_SEQUENTIAL = os.environ.get(
            "BACKUP_SEQUENTIAL_LIBRARIES", "orderbook,optstk"
        ).split(",")
        self.LIBRARIES_TO_BACKUP_FULL = (
            os.environ.get("BACKUP_FULL_LIBRARIES", "").split(",")
            if os.environ.get("BACKUP_FULL_LIBRARIES")
            else []
        )
        self.STATE_FILE = os.path.join(self.MOUNT_POINT, "backup_state.json")
        self.BATCH_SIZE = int(os.environ.get("BACKUP_BATCH_SIZE", "20"))
        self.FULL_BACKUP_BATCH_SIZE = int(os.environ.get("BACKUP_FULL_BATCH_SIZE", "5"))
        self.CPU_THREADS = int(os.environ.get("BACKUP_CPU_THREADS", "16"))
        self.ARCTIC_ENDPOINT = os.environ.get(
            "BACKUP_ARCTIC_ENDPOINT",
            "s3://*************:9000:kivi-arcticdb?access=minioreader&secret=minioreader",
        )


config = BackupConfig()


def load_state() -> Dict:
    """Load backup state from JSON file.

    Returns:
        Dict: Backup state dictionary, empty if file not found.
    """
    try:
        return json.load(open(config.STATE_FILE))
    except FileNotFoundError:
        return {}


def save_state(state: Dict) -> None:
    """Save backup state to JSON file.

    Args:
        state: Backup state dictionary to save.
    """
    os.makedirs(os.path.dirname(config.STATE_FILE), exist_ok=True)
    json.dump(state, open(config.STATE_FILE, "w"), indent=2)


class BackupMetrics:
    """Track backup metrics and performance."""

    def __init__(self):
        """Initialize backup metrics tracking."""
        self.start_time = time.time()
        self.libraries_processed = 0
        self.symbols_processed = 0
        self.symbols_failed = 0
        self.errors = []

    def add_success(self):
        """Record a successful symbol backup."""
        self.symbols_processed += 1

    def add_failure(self, symbol: str, error: str):
        """Record a failed symbol backup.

        Args:
            symbol: Symbol that failed to backup.
            error: Error message describing the failure.
        """
        self.symbols_failed += 1
        self.errors.append(f"{symbol}: {error}")

    def get_summary(self) -> Dict:
        """Get backup metrics summary.

        Returns:
            Dict: Summary of backup metrics including duration, success rate, and errors.
        """
        duration = time.time() - self.start_time
        return {
            "duration_minutes": round(duration / 60, 2),
            "libraries_processed": self.libraries_processed,
            "symbols_processed": self.symbols_processed,
            "symbols_failed": self.symbols_failed,
            "success_rate": round(
                self.symbols_processed
                / max(1, self.symbols_processed + self.symbols_failed)
                * 100,
                2,
            ),
            "avg_throughput_rows_per_min": round(
                self.total_rows_backed_up / max(1, duration / 60), 0
            ),
            "errors": self.errors[:10],
        }


def setup_logging(start_date: pd.Timestamp, end_date: pd.Timestamp) -> None:
    """Setup logging configuration with date-based log files.

    Args:
        start_date: Start date for the backup operation.
        end_date: End date for the backup operation.
    """
    # General log file: YYYYMMDD_YYYYMMDD.log
    log_file_path = os.path.join(
        config.MOUNT_POINT,
        f"logs/{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.log",
    )
    os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

    logging.basicConfig(
        filename=log_file_path,
        level=logging.DEBUG,
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # Error log file: backup_errors_YYYYMMDD_YYYYMMDD.log
    error_log_path = os.path.join(
        config.MOUNT_POINT,
        f"logs/backup_errors_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.log_{end_date.strftime('%Y%m%d')}",
    )

    # Create separate handler for capturing error and above
    error_handler = logging.FileHandler(error_log_path)
    error_handler.setLevel(logging.ERROR)
    error_formatter = logging.Formatter(
        "%(asctime)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
    )
    error_handler.setFormatter(error_formatter)

    logging.getLogger().addHandler(error_handler)


def cleanup_old_error_logs() -> None:
    """Clean up error logs older than 30 days."""
    logs_dir = os.path.join(config.MOUNT_POINT, "logs")
    if not os.path.exists(logs_dir):
        return

    cutoff_date = pd.Timestamp.now() - pd.Timedelta(days=30)

    for filename in os.listdir(logs_dir):
        if filename.startswith("backup_errors_") and filename.endswith(".log"):
            file_path = os.path.join(logs_dir, filename)
            try:
                date_part = filename.replace("backup_errors_", "").replace(".log", "")
                start_date_str = date_part.split("_")[0]
                file_date = pd.Timestamp.strptime(start_date_str, "%Y%m%d")

                if file_date < cutoff_date:
                    os.remove(file_path)
                    logging.info(
                        f"[cleanup_old_error_logs] Removed old error log: {filename}"
                    )
            except (ValueError, OSError, IndexError) as e:
                logging.warning(
                    f"[cleanup_old_error_logs] Failed to process error log {filename}: {e}"
                )


def get_arctic_store() -> Arctic:
    """Get configured Arctic store instance.

    Returns:
        Arctic: Configured Arctic store with CPU thread settings.
    """
    store = Arctic(config.ARCTIC_ENDPOINT)
    set_config_int("VersionStore.NumCPUThreads", config.CPU_THREADS)
    return store


def write_parquet(
    lib_name: str, symbol: str, date: pd.Timestamp, df: pd.DataFrame, metrics=None
) -> bool:
    """Write DataFrame to parquet file.

    Args:
        lib_name: Name of the library.
        symbol: Symbol name.
        date: Date for the data.
        df: DataFrame to write.
        metrics: Optional metrics tracker.

    Returns:
        bool: True if write successful, False otherwise
    """
    year = date.isocalendar().year
    out_dir = os.path.join(config.MOUNT_POINT, lib_name, symbol, str(year))
    os.makedirs(out_dir, exist_ok=True)

    out_file = os.path.join(out_dir, f"{date.strftime('%Y%m%d')}.parquet")

    try:
        df.to_parquet(out_file)

        if metrics:
            metrics.add_success()

        logging.info(
            f"[write_parquet] Library: {lib_name}, Symbol: {symbol}, parquet written ({len(df)} rows)"
        )
        return True
    except Exception as e:
        if metrics:
            metrics.add_failure(f"{lib_name}/{symbol}", str(e))

        logging.error(
            f"[write_parquet] Library: {lib_name}, Symbol: {symbol}, Error: {e}"
        )

        return False


def export_symbols_full_historical(
    lib: Library,
    lib_name: str,
    symbols: List[str],
    metrics=None,
) -> None:
    """Export full historical data for symbols.

    Args:
        lib: Arctic library instance.
        lib_name: Name of the library.
        symbols: List of symbols to export.
        metrics: Optional metrics tracker.
    """
    logging.info(
        f"[export_symbols_full_historical] Library: {lib_name}, Starting full historical backup for {len(symbols)} symbols"
    )

    # Determine the overall date range for all symbols
    try:
        logging.info(
            f"[export_symbols_full_historical] Library: {lib_name}, Determining historical date range for full backup"
        )
        earliest_date = get_library_start_date(lib, lib_name, symbols)
        if earliest_date is None:
            error_msg = "Could not determine start date for full historical backup"
            logging.error(
                f"[export_symbols_full_historical] Library: {lib_name}, Symbol: ALL_SYMBOLS, Error: {error_msg}"
            )
            return

        latest_date = pd.Timestamp.now(tz="Asia/Kolkata").normalize().tz_localize(
            None
        ) - pd.Timedelta(days=1)
        logging.info(
            f"[export_symbols_full_historical] Library: {lib_name}, Full historical backup date range: {earliest_date.date()} → {latest_date.date()}"
        )

    except Exception as e:
        logging.error(
            f"[export_symbols_full_historical] Library: {lib_name}, Symbol: ALL_SYMBOLS, Error: {e}"
        )
        return

    # Process data week by week
    original_full_backup_libs = config.LIBRARIES_TO_BACKUP_FULL[:]
    try:
        # Temporarily remove this library from full backup list to avoid infinite recursion
        config.LIBRARIES_TO_BACKUP_FULL = [
            lib_backup
            for lib_backup in config.LIBRARIES_TO_BACKUP_FULL
            if lib_backup != lib_name
        ]

        for week_start, week_end in list_weeks(earliest_date.normalize(), latest_date):
            logging.info(
                f"[export_symbols_full_historical] Library: {lib_name}, Processing historical week {week_start.date()} → {week_end.date()}"
            )

            try:
                export_symbols_week(
                    lib,
                    lib_name,
                    week_start,
                    week_end,
                    symbols,
                    metrics,
                    is_full_historical=True,
                )
            except Exception as e:
                logging.error(
                    f"[export_symbols_full_historical] Library: {lib_name}, Symbol: WEEK_PROCESSING, Error: {e}"
                )
                continue

    finally:
        # Restore original configuration
        config.LIBRARIES_TO_BACKUP_FULL = original_full_backup_libs

    logging.info(
        f"[export_symbols_full_historical] Library: {lib_name}, Full historical backup completed for {len(symbols)} symbols"
    )


def export_symbols_week(
    lib: Library,
    lib_name: str,
    start_date: pd.Timestamp,
    end_date: pd.Timestamp,
    symbols: List[str],
    metrics=None,
    is_full_historical=False,
) -> None:
    """Export symbols data for a specific week.

    Args:
        lib: Arctic library instance.
        lib_name: Name of the library.
        start_date: Start date for the week.
        end_date: End date for the week.
        symbols: List of symbols to export.
        metrics: Optional metrics tracker.
        is_full_historical: Whether this is part of full historical backup.
    """
    if any(lib_keyword in lib_name for lib_keyword in config.LIBRARIES_TO_BACKUP_FULL):
        logging.info(
            f"[export_symbols_week] Libary: {lib_name}, Library configured for full historical backup"
        )
        export_symbols_full_historical(lib, lib_name, symbols, metrics)
        return

    if any(
        lib_keyword in lib_name for lib_keyword in config.LIBRARIES_TO_SYNC_SEQUENTIAL
    ):
        export_symbols_week_sequential(
            lib, lib_name, start_date, end_date, symbols, metrics, is_full_historical
        )
        return

    end_arctic = end_date + pd.Timedelta(hours=23, minutes=59)
    symbols = [s for s in symbols]

    batch_size = (
        config.FULL_BACKUP_BATCH_SIZE if is_full_historical else config.BATCH_SIZE
    )

    for i in range(0, len(symbols), batch_size):
        batch = symbols[i : i + batch_size]
        requests = [
            ReadRequest(symbol=s, date_range=(start_date, end_arctic)) for s in batch
        ]

        logging.debug(
            f"[export_symbols_week] Library: {lib_name}, batch: {i}, date_range=({start_date}, {end_arctic})"
        )
        results = lib.read_batch(requests)
        for vi in results:
            if isinstance(vi, DataError):
                logging.error(
                    f"[export_symbols_week] Library: {lib_name}, Symbol: {vi.symbol}, Date: {start_date.date()}, Error: DataError"
                )
                continue

            df: pd.DataFrame = vi.data
            if df is None or df.empty:
                continue

            success = write_parquet(lib_name, vi.symbol, start_date, df, metrics)
            if not success:
                logging.error(
                    f"[export_symbols_week] Library: {lib_name}, Symbol: {vi.symbol}, Date: {start_date.date()}, Error: Write failure"
                )

            del df

        del results
        gc.collect()


def export_symbols_week_sequential(
    lib: Library,
    lib_name: str,
    start_date: pd.Timestamp,
    end_date: pd.Timestamp,
    symbols: List[str],
    metrics=None,
) -> None:
    """Export symbols data sequentially for a specific week.

    Args:
        lib: Arctic library instance.
        lib_name: Name of the library.
        start_date: Start date for the week.
        end_date: End date for the week.
        symbols: List of symbols to export.
        metrics: Optional metrics tracker.
    """
    end_arctic = end_date + pd.Timedelta(hours=23, minutes=59)

    for sym in symbols:
        try:
            logging.debug(
                f"[export_symbols_week_sequential] Library: {lib_name}, Symbol: {sym}, date_range: ({start_date}, {end_arctic})"
            )
            vi = lib.read(
                sym,
                date_range=(start_date, end_arctic),
            )
            df: pd.DataFrame = vi.data
        except Exception as e:
            logging.error(
                f"[export_symbols_week_sequential] Library: {lib_name}, Symbol: {sym}, Date: {start_date.date()}, Error: {e}"
            )
            continue

        if df is None or df.empty:
            continue

        success = write_parquet(lib_name, sym, start_date, df, metrics)
        if not success:
            logging.error(
                f"[export_symbols_week_sequential] Library: {lib_name}, Symbol: {sym}, Date: {start_date.date()}, Error: Write failure"
            )

        del df, vi
        gc.collect()


def export_metadata(lib: Library, lib_name: str, symbols: List[str]) -> None:
    """Export metadata for symbols to pickle files.

    Args:
        lib: Arctic library instance.
        lib_name: Name of the library.
        symbols: List of symbols to export metadata for.
    """
    logging.debug(f"[export_metadata] Library: {lib_name}")
    requests = [ReadInfoRequest(symbol=sym) for sym in symbols]
    results = lib.read_metadata_batch(requests)

    for vi in results:
        if isinstance(vi, DataError):
            logging.error(
                f"[export_metadata] Library: {lib_name}, Symbol: {vi.symbol}, read error, skipping"
            )
            continue

        meta_dict = vi.metadata
        out_dir = os.path.join(config.MOUNT_POINT, lib_name, vi.symbol)
        os.makedirs(out_dir, exist_ok=True)
        path = os.path.join(out_dir, "metadata.pkl")

        with open(path, "wb") as f:
            pickle.dump(meta_dict, f, protocol=pickle.HIGHEST_PROTOCOL)
            logging.info(
                f"[export_metadata] Library: {lib_name}, Symbol: {vi.symbol}, metadata written"
            )

        del meta_dict

    del results
    gc.collect()


def get_library_start_date(
    lib: Library, lib_name: str, symbols: List[str]
) -> pd.Timestamp:
    """Get the earliest start date across all symbols in a library.

    Args:
        lib: Arctic library instance.
        lib_name: Name of the library.
        symbols: List of symbols to check.

    Returns:
        pd.Timestamp: Earliest start date, or None if no valid dates found.
    """
    first_dates: List[pd.Timestamp] = []
    for i in range(0, len(symbols), config.BATCH_SIZE):
        batch = symbols[i : i + config.BATCH_SIZE]
        logging.debug(
            f"[get_library_start_date] Library: {lib_name}, batch: {i}, get description"
        )
        results = lib.get_description_batch(batch)

        for desc in results:
            if isinstance(desc, DataError):
                logging.error(
                    f"[get_library_start_date] Library: {lib_name}, {desc.symbol}, DataError, skipping"
                )
                continue

            start_date_utc = pd.Timestamp(desc.date_range[0])
            start_date_ist = start_date_utc.tz_convert("Asia/Kolkata")

            first_dates.append(start_date_ist)

        del results
        gc.collect()

    if first_dates:
        return min(first_dates)
    else:
        return None


def list_weeks(start: pd.Timestamp, end: pd.Timestamp):
    """Generate weekly date ranges between start and end dates.

    Args:
        start: Start date.
        end: End date.

    Yields:
        Tuple[pd.Timestamp, pd.Timestamp]: Weekly start and end date pairs.
    """
    cur = start - pd.Timedelta(days=start.weekday())
    while cur < end:
        nxt = cur + pd.Timedelta(days=7)
        yield cur, min(nxt, end)
        cur = nxt


def rename_symbol_dir_to_backup(lib_name: str, symbols: List[str]) -> None:
    """Rename symbol directories to backup directories.

    Args:
        lib_name: Name of the library.
        symbols: List of symbols to backup.

    Raises:
        RuntimeError: If backup directory already exists.
    """
    for sym in symbols:
        symbol_dir = os.path.join(config.MOUNT_POINT, lib_name, sym)
        bak_dir = symbol_dir + ".bak"
        if os.path.exists(bak_dir):
            raise RuntimeError(
                f"Backup directory already exists: {bak_dir}. Refusing to overwrite."
            )
        if os.path.exists(symbol_dir):
            os.rename(symbol_dir, bak_dir)


def delete_symbol_backup_dir(lib_name: str, symbols: List[str]) -> None:
    """Delete backup directories for symbols.

    Args:
        lib_name: Name of the library.
        symbols: List of symbols whose backup directories to delete.
    """
    for sym in symbols:
        bak_dir = os.path.join(config.MOUNT_POINT, lib_name, sym) + ".bak"
        if os.path.exists(bak_dir):
            shutil.rmtree(bak_dir)


def main() -> None:
    metrics = BackupMetrics()
    state = load_state()
    last_end_str = state.get("last_end")
    curr_end_str = state.get("current_end")
    if curr_end_str:
        end_date = pd.Timestamp(curr_end_str)
    else:
        end_date = pd.Timestamp.now(tz="Asia/Kolkata").normalize().tz_localize(
            None
        ) - pd.Timedelta(days=1)
        state["current_end"] = end_date.strftime("%Y%m%d")
        save_state(state)

    start_date = pd.Timestamp(last_end_str) + pd.Timedelta(days=1)
    if start_date > end_date:
        state.pop("current_end", None)
        save_state(state)
        return

    setup_logging(start_date, end_date)

    cleanup_old_error_logs()

    backed_libs = set(state.get("backed_libraries", []))
    logging.info(f"[main] Backing up data from {start_date.date()} → {end_date.date()}")
    logging.info(f"[main] Already done: {sorted(backed_libs)}")
    logging.info(f"[main] Configuration: {vars(config)}")
    logging.info(f"[main] Full backup libraries: {config.LIBRARIES_TO_BACKUP_FULL}")

    store = get_arctic_store()
    for lib_name in store.list_libraries():
        if any(
            lib_keyword in lib_name.lower() for lib_keyword in config.LIBRARIES_TO_SKIP
        ):
            continue
        if lib_name in backed_libs:
            continue

        lib = store[lib_name]
        export_symbols_week(
            lib, lib_name, start_date, end_date, lib.list_symbols(), metrics
        )
        export_metadata(lib, lib_name, lib.list_symbols())

        metrics.libraries_processed += 1
        backed_libs.add(lib_name)
        state["backed_libraries"] = list(backed_libs)
        save_state(state)

    audit_lib = store["operation_audit_log"]
    end_arctic = end_date + pd.Timedelta(hours=23, minutes=59)
    updated_libs = set(state.get("updated_libraries", []))

    updated_dict: Dict[str, List[str]] = {}
    for lib_name in audit_lib.list_symbols():
        if lib_name in updated_libs:
            continue

        logging.debug(
            f"[main] Library: operation_audit_log, Symbol: {lib_name}, date_range: ({start_date}, {end_arctic})"
        )
        vi = audit_lib.read(lib_name, date_range=(start_date, end_arctic))
        if isinstance(vi, DataError):
            logging.warning(
                f"[main] Library: operation_audit_log, Symbol: {lib_name}, DataError, skipping"
            )
            continue

        df: pd.DataFrame = vi.data
        updated = df.loc[df.operation_type == "update", "symbol"].unique().tolist()

        if not updated:
            continue

        updated_dict[lib_name] = updated
        logging.info(f"[main] Library: {lib_name}, Detected updates")

    if updated_dict:
        for lib_name, symbols in updated_dict.items():
            if lib_name in updated_libs:
                continue

            lib = store[lib_name]
            library_start = get_library_start_date(lib, lib_name, symbols)

            logging.info(
                f"[main] Libaray: {lib_name}, Backfilling from {library_start.date()} to {end_date.date()}"
            )

            rename_symbol_dir_to_backup(lib_name, symbols)
            for week_start, week_end in list_weeks(library_start.normalize(), end_date):
                logging.info(
                    f"[main] Library: {lib_name}, Week: {week_start.date()} → {week_end.date()}"
                )
                export_symbols_week(
                    lib, lib_name, week_start, week_end, symbols, metrics
                )
            export_metadata(lib, lib_name, symbols)

            updated_libs.add(lib_name)
            state["updated_libraries"] = list(updated_libs)
            delete_symbol_backup_dir(lib_name, symbols)
            save_state(state)

    state["last_end"] = end_date.strftime("%Y%m%d")
    state.pop("backed_libraries", None)
    state.pop("updated_libraries", None)
    state.pop("current_end", None)
    save_state(state)

    # Log final metrics
    summary = metrics.get_summary()
    logging.info(f"[main] Backup completed: {summary}")
    logging.info(f"[main] Backup Summary: {json.dumps(summary, indent=2)}")

    if len(summary["errors"]) > 0:
        raise Exception(
            f"Backup failed with {len(summary['errors'])} errors, check error logs for detailed information"
        )


if __name__ == "__main__":
    try:
        main()
        sys.exit(0)
    except Exception as e:
        logging.error(f"[__main__] Unhandled failure in main: {e}")
        sys.exit(1)
