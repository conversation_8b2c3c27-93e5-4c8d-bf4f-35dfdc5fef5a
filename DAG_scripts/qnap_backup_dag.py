"""
Airflow DAG for QNAP Arctic Data Backup

This DAG runs the qnap_backup.py script on a weekly schedule to backup
ArcticDB data to QNAP storage with proper monitoring and alerting.
"""

import datetime
import os
import sys
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.email_operator import EmailOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable

# Add the project root to Python path
sys.path.append('/home/<USER>/repos/data_auditing')

# Import the backup script
import qnap_backup

# Default arguments for the DAG
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': datetime.timedelta(minutes=30),
    'email': [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ]
}

# Create the DAG
dag = DAG(
    'qnap_arctic_backup',
    default_args=default_args,
    description='Weekly backup of ArcticDB data to QNAP storage',
    schedule_interval='0 2 * * 0',  # Run every Sunday at 2 AM
    catchup=False,
    max_active_runs=1,  # Ensure only one backup runs at a time
    tags=['backup', 'arctic', 'qnap', 'weekly']
)

def check_prerequisites(**context):
    """Check if backup prerequisites are met."""
    import subprocess
    import logging
    
    # Check if QNAP mount is available
    mount_point = qnap_backup.config.MOUNT_POINT
    if not os.path.ismount(mount_point):
        raise Exception(f"QNAP mount point {mount_point} is not available")
    
    # Check available disk space (require at least 100GB free)
    statvfs = os.statvfs(mount_point)
    free_bytes = statvfs.f_frsize * statvfs.f_bavail
    free_gb = free_bytes / (1024**3)
    
    if free_gb < 100:
        raise Exception(f"Insufficient disk space on QNAP: {free_gb:.1f}GB available, need at least 100GB")
    
    # Check if ArcticDB is accessible
    try:
        store = qnap_backup.get_arctic_store()
        libraries = store.list_libraries()
        logging.info(f"ArcticDB accessible with {len(libraries)} libraries")
    except Exception as e:
        raise Exception(f"Cannot access ArcticDB: {e}")
    
    logging.info(f"Prerequisites check passed. Free space: {free_gb:.1f}GB")
    return True

def run_backup(**context):
    """Execute the backup process."""
    import logging
    
    try:
        # Run the main backup function
        qnap_backup.main()
        logging.info("Backup completed successfully")
        return "success"
    except Exception as e:
        logging.error(f"Backup failed: {e}")
        raise

def cleanup_old_backups(**context):
    """Clean up old backup files to manage disk space."""
    import glob
    import time
    import logging
    
    mount_point = qnap_backup.config.MOUNT_POINT
    
    # Remove backup files older than 90 days
    cutoff_time = time.time() - (90 * 24 * 60 * 60)
    
    for root, dirs, files in os.walk(mount_point):
        for file in files:
            if file.endswith('.parquet'):
                file_path = os.path.join(root, file)
                if os.path.getmtime(file_path) < cutoff_time:
                    try:
                        os.remove(file_path)
                        logging.info(f"Removed old backup file: {file_path}")
                    except Exception as e:
                        logging.warning(f"Failed to remove {file_path}: {e}")
    
    # Remove empty directories
    for root, dirs, files in os.walk(mount_point, topdown=False):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            try:
                if not os.listdir(dir_path):
                    os.rmdir(dir_path)
                    logging.info(f"Removed empty directory: {dir_path}")
            except Exception as e:
                logging.warning(f"Failed to remove directory {dir_path}: {e}")

def generate_backup_report(**context):
    """Generate and send backup completion report."""
    import json
    import logging
    
    # Read the latest log file to extract metrics
    mount_point = qnap_backup.config.MOUNT_POINT
    log_dir = os.path.join(mount_point, "logs")
    
    if os.path.exists(log_dir):
        log_files = sorted([f for f in os.listdir(log_dir) if f.endswith('.log')])
        if log_files:
            latest_log = os.path.join(log_dir, log_files[-1])
            with open(latest_log, 'r') as f:
                log_content = f.read()
            
            # Extract summary from log
            if "Backup Summary:" in log_content:
                summary_start = log_content.find("Backup Summary:")
                summary_json = log_content[summary_start + len("Backup Summary:"):].strip()
                try:
                    summary = json.loads(summary_json.split('\n')[0])
                    context['task_instance'].xcom_push(key='backup_summary', value=summary)
                    logging.info(f"Backup report generated: {summary}")
                except json.JSONDecodeError:
                    logging.warning("Could not parse backup summary from log")

# Define tasks
check_prerequisites_task = PythonOperator(
    task_id='check_prerequisites',
    python_callable=check_prerequisites,
    dag=dag,
)

backup_task = PythonOperator(
    task_id='run_backup',
    python_callable=run_backup,
    dag=dag,
)

cleanup_task = PythonOperator(
    task_id='cleanup_old_backups',
    python_callable=cleanup_old_backups,
    dag=dag,
)

report_task = PythonOperator(
    task_id='generate_backup_report',
    python_callable=generate_backup_report,
    dag=dag,
)

# Health check task to verify backup integrity
health_check_task = BashOperator(
    task_id='health_check',
    bash_command=f"""
    # Check if backup state file exists and is valid
    if [ -f "{qnap_backup.config.STATE_FILE}" ]; then
        echo "Backup state file exists"
        python3 -c "import json; json.load(open('{qnap_backup.config.STATE_FILE}'))"
        echo "Backup state file is valid JSON"
    else
        echo "Warning: Backup state file not found"
        exit 1
    fi
    
    # Check recent backup files
    find {qnap_backup.config.MOUNT_POINT} -name "*.parquet" -mtime -7 | head -10
    """,
    dag=dag,
)

success_notification = DummyOperator(
    task_id='backup_success',
    dag=dag,
)

# Define task dependencies
check_prerequisites_task >> backup_task >> [cleanup_task, report_task, health_check_task] >> success_notification
